<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Robotics Research - AI Research Whitepaper News</title>
  <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700;900&family=Merriweather:wght@300;400;700&family=Open+Sans:wght@300;400;600&display=swap" rel="stylesheet">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Merriweather', serif;
      line-height: 1.6;
      color: #333;
      background: #fff;
    }

    /* Header Section */
    .header {
      border-bottom: 3px solid #000;
      padding: 1rem 0;
      background: #fff;
      margin-top: 60px; /* Account for fixed nav-bar height */
    }

    .nav-bar {
      background: #000;
      color: #fff;
      padding: 0.5rem 0;
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: 1000;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .nav-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 1rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .nav-links {
      display: flex;
      gap: 2rem;
      list-style: none;
    }

    .nav-links a {
      color: #fff;
      text-decoration: none;
      font-family: 'Open Sans', sans-serif;
      font-weight: 600;
      font-size: 0.9rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .nav-links a:hover {
      color: #ccc;
    }

    .nav-links a.active {
      color: #4a90e2;
      border-bottom: 2px solid #4a90e2;
      padding-bottom: 2px;
    }

    .masthead {
      text-align: center;
      padding: 2rem 0 1rem;
      max-width: 1200px;
      margin: 0 auto;
    }

    .newspaper-title {
      font-family: 'Playfair Display', serif;
      font-size: 4rem;
      font-weight: 900;
      color: #000;
      margin-bottom: 0.5rem;
      letter-spacing: 2px;
    }

    .date-line {
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-top: 1px solid #000;
      border-bottom: 1px solid #000;
      padding: 0.5rem 0;
      margin: 1rem 2rem 0;
      font-family: 'Open Sans', sans-serif;
      font-size: 0.9rem;
    }

    .tagline {
      font-style: italic;
      font-weight: 300;
    }

    /* Main Content */
    .main-content {
      max-width: 1200px;
      margin: 0 auto;
      padding: 2rem 1rem;
      display: grid;
      grid-template-columns: 2fr 1fr;
      gap: 2rem;
    }

    .news-section {
      display: grid;
      gap: 2rem;
    }

    /* Featured Article */
    .featured-article {
      border-bottom: 2px solid #000;
      padding-bottom: 2rem;
      margin-bottom: 2rem;
    }

    .featured-article .category-tag {
      background: #000;
      color: #fff;
      padding: 0.3rem 0.8rem;
      font-family: 'Open Sans', sans-serif;
      font-size: 0.8rem;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 1px;
      display: inline-block;
      margin-bottom: 1rem;
    }

    .featured-article h1 {
      font-family: 'Playfair Display', serif;
      font-size: 2.5rem;
      font-weight: 700;
      line-height: 1.2;
      margin-bottom: 1rem;
      color: #000;
    }

    .featured-article .byline {
      font-family: 'Open Sans', sans-serif;
      font-size: 0.9rem;
      color: #666;
      margin-bottom: 1rem;
    }

    .featured-article .summary {
      font-size: 1.1rem;
      line-height: 1.7;
      margin-bottom: 1rem;
      color: #444;
    }

    .featured-article .read-more {
      color: #0066cc;
      text-decoration: none;
      font-weight: 600;
      font-family: 'Open Sans', sans-serif;
    }

    /* Featured Article Image */
    .featured-image {
      float: right;
      width: 300px;
      margin: 0 0 1rem 2rem;
      clear: right;
    }

    .article-img {
      width: 100%;
      height: 200px;
      object-fit: cover;
      border-radius: 8px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease;
    }

    .article-img:hover {
      transform: scale(1.02);
    }

    /* Photo Credits */
    .photo-credit {
      font-family: 'Open Sans', sans-serif;
      font-size: 0.7rem;
      color: #888;
      margin-top: 0.3rem;
      text-align: right;
    }

    .photo-credit a {
      color: #888;
      text-decoration: none;
    }

    .photo-credit a:hover {
      color: #0066cc;
      text-decoration: underline;
    }

    .photo-credit-small {
      font-family: 'Open Sans', sans-serif;
      font-size: 0.6rem;
      color: #999;
      margin-top: 0.2rem;
      text-align: center;
    }

    .photo-credit-small a {
      color: #999;
      text-decoration: none;
    }

    .photo-credit-small a:hover {
      color: #0066cc;
      text-decoration: underline;
    }

    /* Article Grid */
    .articles-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2rem;
    }

    .article {
      border-bottom: 1px solid #ddd;
      padding-bottom: 1.5rem;
    }

    .article h2 {
      font-family: 'Playfair Display', serif;
      font-size: 1.4rem;
      font-weight: 700;
      line-height: 1.3;
      margin-bottom: 0.8rem;
      color: #000;
    }

    .article .summary {
      font-size: 0.95rem;
      line-height: 1.6;
      margin-bottom: 0.8rem;
      color: #555;
    }

    .article .read-more {
      color: #0066cc;
      text-decoration: none;
      font-weight: 600;
      font-family: 'Open Sans', sans-serif;
      font-size: 0.9rem;
    }

    /* Article Thumbnails */
    .article-thumbnail {
      float: left;
      width: 120px;
      margin: 0 1rem 1rem 0;
      clear: left;
    }

    .thumbnail-img {
      width: 100%;
      height: 80px;
      object-fit: cover;
      border-radius: 6px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease;
    }

    .thumbnail-img:hover {
      transform: scale(1.05);
    }

    /* Sidebar */
    .sidebar {
      border-left: 1px solid #ddd;
      padding-left: 2rem;
    }

    .sidebar-section {
      margin-bottom: 2rem;
      padding-bottom: 2rem;
      border-bottom: 1px solid #eee;
    }

    .sidebar-section:last-child {
      border-bottom: none;
    }

    .sidebar-title {
      font-family: 'Playfair Display', serif;
      font-size: 1.3rem;
      font-weight: 700;
      margin-bottom: 1rem;
      color: #000;
      border-bottom: 2px solid #000;
      padding-bottom: 0.5rem;
    }

    .trending-item {
      margin-bottom: 1rem;
      padding-bottom: 1rem;
      border-bottom: 1px solid #f0f0f0;
    }

    .trending-item:last-child {
      border-bottom: none;
    }

    .trending-item h3 {
      font-family: 'Open Sans', sans-serif;
      font-size: 0.95rem;
      font-weight: 600;
      line-height: 1.4;
      margin-bottom: 0.3rem;
    }

    .trending-item a {
      color: #333;
      text-decoration: none;
    }

    .trending-item a:hover {
      color: #0066cc;
    }

    /* Sidebar Articles */
    .sidebar-article {
      margin-bottom: 1.5rem;
      padding-bottom: 1.5rem;
      border-bottom: 1px solid #f0f0f0;
    }

    .sidebar-article:last-child {
      border-bottom: none;
    }

    .sidebar-article h3 {
      font-family: 'Playfair Display', serif;
      font-size: 1.1rem;
      font-weight: 700;
      line-height: 1.3;
      margin-bottom: 0.5rem;
      color: #000;
    }

    .sidebar-article h3 a {
      color: #000;
      text-decoration: none;
    }

    .sidebar-article h3 a:hover {
      color: #0066cc;
    }

    .sidebar-article .summary {
      font-size: 0.85rem;
      line-height: 1.5;
      margin-bottom: 0.5rem;
      color: #666;
      text-align: justify;
    }

    .sidebar-article .read-more {
      color: #0066cc;
      text-decoration: none;
      font-weight: 600;
      font-family: 'Open Sans', sans-serif;
      font-size: 0.8rem;
    }

    .sidebar-article .read-more:hover {
      text-decoration: underline;
    }

    /* Sidebar Article Thumbnails */
    .sidebar-thumbnail {
      width: 100%;
      margin-bottom: 0.8rem;
    }

    .sidebar-thumbnail-img {
      width: 100%;
      height: 120px;
      object-fit: cover;
      border-radius: 6px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease;
    }

    .sidebar-thumbnail-img:hover {
      transform: scale(1.02);
    }

    /* Footer */
    .footer {
      border-top: 2px solid #000;
      padding: 2rem 0;
      text-align: center;
      background: #f8f8f8;
      margin-top: 3rem;
    }

    .footer p {
      font-family: 'Open Sans', sans-serif;
      font-size: 0.9rem;
      color: #666;
    }

    /* Mobile Responsive */
    @media (max-width: 768px) {
      .newspaper-title {
        font-size: 2.5rem;
      }

      .main-content {
        grid-template-columns: 1fr;
        padding: 1rem;
      }

      .sidebar {
        border-left: none;
        border-top: 2px solid #000;
        padding-left: 0;
        padding-top: 2rem;
        margin-top: 2rem;
      }

      .featured-article h1 {
        font-size: 2rem;
      }

      .articles-grid {
        grid-template-columns: 1fr;
      }

      .nav-links {
        flex-wrap: wrap;
        gap: 1rem;
      }

      .date-line {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
      }

      /* Mobile image adjustments */
      .featured-image {
        float: none;
        width: 100%;
        margin: 1rem 0;
      }

      .article-thumbnail {
        float: none;
        width: 100%;
        margin: 0 0 1rem 0;
      }

      .thumbnail-placeholder {
        height: 120px;
      }
    }

    @media (max-width: 480px) {
      .newspaper-title {
        font-size: 2rem;
      }

      .featured-article h1 {
        font-size: 1.6rem;
      }

      .nav-container {
        flex-direction: column;
        gap: 1rem;
      }
    }

    /* Dark Mode Toggle */
    .dark-mode-toggle {
      display: flex;
      align-items: center;
    }

    .toggle-btn {
      background: none;
      border: none;
      color: #fff;
      cursor: pointer;
      padding: 8px;
      border-radius: 50%;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .toggle-btn:hover {
      background: rgba(255, 255, 255, 0.1);
      transform: scale(1.1);
    }

    .lightbulb-icon {
      transition: all 0.3s ease;
    }

    .toggle-btn:hover .lightbulb-icon {
      filter: drop-shadow(0 0 8px #ffd700);
    }

    /* Dark Mode Styles */
    :root {
      --bg-color: #fff;
      --text-color: #333;
      --header-bg: #fff;
      --border-color: #000;
      --light-border: #ddd;
      --sidebar-border: #ddd;
      --footer-bg: #f8f8f8;
      --article-border: #ddd;
      --byline-color: #666;
      --summary-color: #444;
      --sidebar-summary-color: #666;
      --trending-border: #f0f0f0;
    }

    [data-theme="dark"] {
      --bg-color: #1a1a1a;
      --text-color: #e0e0e0;
      --header-bg: #1a1a1a;
      --border-color: #444;
      --light-border: #333;
      --sidebar-border: #333;
      --footer-bg: #111;
      --article-border: #333;
      --byline-color: #aaa;
      --summary-color: #ccc;
      --sidebar-summary-color: #aaa;
      --trending-border: #2a2a2a;
    }

    body {
      background: var(--bg-color);
      color: var(--text-color);
      transition: background-color 0.3s ease, color 0.3s ease;
    }

    .header {
      background: var(--header-bg);
      border-bottom: 3px solid var(--border-color);
    }

    .newspaper-title {
      color: var(--text-color);
    }

    .date-line {
      border-top: 1px solid var(--border-color);
      border-bottom: 1px solid var(--border-color);
    }

    .featured-article {
      border-bottom: 2px solid var(--border-color);
    }

    .featured-article h1 {
      color: var(--text-color);
    }

    .featured-article .byline {
      color: var(--byline-color);
    }

    .featured-article .summary {
      color: var(--summary-color);
    }

    .article {
      border-bottom: 1px solid var(--article-border);
    }

    .article h2 {
      color: var(--text-color);
    }

    .article .summary {
      color: var(--summary-color);
    }

    .sidebar {
      border-left: 1px solid var(--sidebar-border);
    }

    .sidebar-section {
      border-bottom: 1px solid var(--trending-border);
    }

    .sidebar-title {
      color: var(--text-color);
      border-bottom: 2px solid var(--border-color);
    }

    .trending-item {
      border-bottom: 1px solid var(--trending-border);
    }

    .trending-item a {
      color: var(--text-color);
    }

    .sidebar-article {
      border-bottom: 1px solid var(--trending-border);
    }

    .sidebar-article h3 {
      color: var(--text-color);
    }

    .sidebar-article h3 a {
      color: var(--text-color);
    }

    .sidebar-article .summary {
      color: var(--sidebar-summary-color);
    }

    .footer {
      background: var(--footer-bg);
      border-top: 2px solid var(--border-color);
    }

    .footer p {
      color: var(--byline-color);
    }

    /* Dark mode mobile adjustments */
    @media (max-width: 768px) {
      .sidebar {
        border-left: none;
        border-top: 2px solid var(--border-color);
      }
    }
  </style>
</head>
<body>
  <!-- Navigation Bar -->
  <nav class="nav-bar">
    <div class="nav-container">
      <ul class="nav-links">
        <li><a href="index.html">AI Research</a></li>
        <li><a href="ml.html">Machine Learning</a></li>
        <li><a href="cv.html">Computer Vision</a></li>
        <li><a href="cr.html">Security/Cryptography</a></li>
        <li><a href="ro.html" class="active">Robotics</a></li>
        <li><a href="hc.html">Man & Machine</a></li>
      </ul>
      <div class="dark-mode-toggle">
        <button id="darkModeToggle" class="toggle-btn" aria-label="Toggle dark mode">
          <svg class="lightbulb-icon" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M9 21c0 .5.4 1 1 1h4c.6 0 1-.5 1-1v-1H9v1zm3-19C8.1 2 5 5.1 5 9c0 2.4 1.2 4.5 3 5.7V17c0 .5.4 1 1 1h6c.6 0 1-.5 1-1v-2.3c1.8-1.2 3-3.3 3-5.7 0-3.9-3.1-7-7-7z"/>
          </svg>
        </button>
      </div>
    </div>
  </nav>

  <!-- Header -->
  <header class="header">
    <div class="masthead">
      <h1 class="newspaper-title">AI Research Whitepaper News</h1>
      <div class="date-line">
        <span class="date">June 20, 2025</span>
        <span class="tagline">Cutting Edge Robotics News For Humans!</span>
        <span class="edition">Digital Edition</span>
      </div>
    </div>
  </header>

  <!-- Main Content -->
  <main class="main-content">
    <section class="news-section">
      
      <article class="featured-article">
        <div class="category-tag">FEATURED ROBOTICS</div>
        <h1>AI Learns Object Dynamics from Limited Camera Views</h1>
        <div class="byline">From arXiv • Latest Research</div>
        <div class="featured-image">
          <img src="images/article_8ba03f73.jpg" alt="time lapse photography of road during nighttime" class="article-img" />
          <div class="photo-credit">
                    <a href="https://unsplash.com/@b_randy?utm_source=arxiv_aggregator&utm_medium=referral" target="_blank">Photo by Randy on Unsplash</a> •
                    <a href="https://unsplash.com/?utm_source=arxiv_aggregator&utm_medium=referral" target="_blank">Unsplash</a>
                </div>
        </div>
        <p class="summary">Scientists developed a new way to simulate how objects change shape and move by combining computer particles with 3D grids.  This approach allows them to create realistic digital models of various objects, even when they have limited information from cameras, which could be useful for robotics and other applications.</p>
        <a href="http://arxiv.org/pdf/2506.15680v1" target="_blank" class="read-more">Read Full Paper →</a>
      </article>

      <div class="articles-grid">
        <article class="article">
          
          <h2>AI Learns by Seeing and Knowing</h2>
          <p class="summary">Scientists created a new way for artificial intelligence to understand and interact with both the digital world (like online information) and physical world (through cameras and sensors).  This innovation has the potential to help AI perform complex tasks that require understanding both online knowledge and real-world environments, which could lead to significant improvements in areas like cooking, navigation, and tourism.</p>
          <a href="http://arxiv.org/pdf/2506.15677v1" target="_blank" class="read-more">Read Paper →</a>
        </article>
        <article class="article">
          
          <h2>Robots Learn to See and Act Like Humans</h2>
          <p class="summary">Scientists created a system called Vision in Action (ViA) that helps robots learn how to manipulate objects more effectively by observing and imitating human actions.  This system could be important because it allows robots to use their own "vision" and movement abilities to perform complex tasks, like picking up small items from hard-to-reach places.</p>
          <a href="http://arxiv.org/pdf/2506.15666v1" target="_blank" class="read-more">Read Paper →</a>
        </article>
        <article class="article">
          
          <div class="article-thumbnail">
            <img src="images/article_3de46893.jpg" alt="man in black crew neck t-shirt wearing black sunglasses holding black smartphone" class="thumbnail-img" />
            <div class="photo-credit-small">
                        <a href="https://unsplash.com/@minhphamdesign?utm_source=arxiv_aggregator&utm_medium=referral" target="_blank">Photo by Minh Pham on Unsplash</a> •
                        <a href="https://unsplash.com/?utm_source=arxiv_aggregator&utm_medium=referral" target="_blank">Unsplash</a>
                    </div>
          </div>
          <h2>AI Learns From Past Experiences To Navigate And Interact With Environment</h2>
          <p class="summary">Scientists created a new way to test how well computers can use past experiences to navigate and interact with their environment.  This benchmark will help developers improve computer systems that need to remember and learn from their actions over time, which is crucial for tasks like object manipulation and navigation in complex environments.</p>
          <a href="http://arxiv.org/pdf/2506.15635v1" target="_blank" class="read-more">Read Paper →</a>
        </article>
        <article class="article">
          
          <h2>Rethinking Robot Grasping with AI-Driven Insights</h2>
          <p class="summary">The researchers created a new system called GRIM that helps robots figure out how to grasp objects in a way that's useful for specific tasks.  This system can be used to help robots perform tasks more efficiently and effectively, without needing extensive training or data, which could have important implications for real-world applications.</p>
          <a href="http://arxiv.org/pdf/2506.15607v1" target="_blank" class="read-more">Read Paper →</a>
        </article>
      </div>

    </section>

    <!-- Sidebar -->
    <aside class="sidebar">
      <div class="sidebar-section">
        <h2 class="sidebar-title">Latest Articles</h2>
                <article class="sidebar-article">
          <h3><a href="http://arxiv.org/pdf/2405.01328v2" target="_blank">BlueIce Revolutionizes Self-Driving Car Simulations with Ultra Realistic Digital Twins</a></h3>
          <p class="summary">Scientists developed a new tool called BlueICE, which is designed to make simulation of self-driving cars more realistic and flexible.  This tool could help improve public safety by allowing researchers to test and refine their autonomous vehicle designs in a virtual environment that's closer to real-world conditions.</p>
          <a href="http://arxiv.org/pdf/2405.01328v2" target="_blank" class="read-more">Read Paper →</a>
        </article>
        <article class="sidebar-article">
          <h3><a href="http://arxiv.org/pdf/2506.15560v1" target="_blank">Radar Revolution Boosts AI Depth Perception</a></h3>
          <p class="summary">Scientists developed a new way to create accurate depth maps using radar signals, without needing expensive and data-intensive supervision from LiDAR sensors.  This breakthrough could have significant practical applications, such as improving self-driving cars' ability to navigate complex environments with greater precision and detail.</p>
          <a href="http://arxiv.org/pdf/2506.15560v1" target="_blank" class="read-more">Read Paper →</a>
        </article>
        <article class="sidebar-article">
          <h3><a href="http://arxiv.org/pdf/2506.15539v1" target="_blank">AI Learns to Master Aerial Grabs with Maximum Efficiency</a></h3>
          <p class="summary">Scientists developed a new planning system for robots with arms that can grasp objects in the air, allowing them to move more efficiently and effectively.  This new system could make it easier for robots to perform complex tasks in tight spaces, potentially opening up new possibilities for applications like search and rescue or assembly line work.</p>
          <a href="http://arxiv.org/pdf/2506.15539v1" target="_blank" class="read-more">Read Paper →</a>
        </article>

      </div>
    </aside>
  </main>

  <!-- Footer -->
  <footer class="footer">
    <p>© 2025 AI Research Whitepaper News. All rights reserved. | Updated June 20, 2025</p>
  </footer>

  <script>
    // Dark mode toggle functionality
    const darkModeToggle = document.getElementById('darkModeToggle');
    const body = document.body;

    // Check for saved theme preference or default to light mode
    const currentTheme = localStorage.getItem('theme') || 'light';
    if (currentTheme === 'dark') {
      body.setAttribute('data-theme', 'dark');
    }

    // Toggle dark mode
    darkModeToggle.addEventListener('click', () => {
      const currentTheme = body.getAttribute('data-theme');
      
      if (currentTheme === 'dark') {
        body.removeAttribute('data-theme');
        localStorage.setItem('theme', 'light');
      } else {
        body.setAttribute('data-theme', 'dark');
        localStorage.setItem('theme', 'dark');
      }
    });
  </script>
</body>
</html>