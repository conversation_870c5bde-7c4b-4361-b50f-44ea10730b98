# Web Search Aggregator Setup Guide

This guide explains how to set up and use the new web search aggregator that creates news pages from internet search results instead of arXiv papers.

## Overview

The web search aggregator (`aggregator_web.py`) extends your existing arXiv aggregator system to include general tech/AI news from across the internet. It uses the same AI-powered content enhancement and visual design as your arXiv aggregators.

## Features

- **Internet Search**: Uses Google Custom Search API to find recent tech/AI news
- **AI Enhancement**: Rewrites headlines and summaries using your existing Ollama setup
- **Visual Content**: Generates relevant images using Unsplash API
- **Consistent Design**: Uses the same newspaper-style template as other aggregators
- **Duplicate Prevention**: Tracks processed articles to avoid republishing
- **Automated Publishing**: Uploads to your FTP server alongside other pages

## Setup Instructions

### 1. Get Google Custom Search API Credentials

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the "Custom Search API"
4. Create credentials (API Key)
5. Set up a Custom Search Engine at [Google CSE](https://cse.google.com/cse/)
6. Configure it to search the entire web
7. Note your Search Engine ID

### 2. Update Environment Variables

Add these lines to your `.env` file:

```env
# Google Search API (for web search functionality)
GOOGLE_SEARCH_API_KEY=your-google-search-api-key
GOOGLE_SEARCH_ENGINE_ID=your-custom-search-engine-id
```

### 3. Test the Setup

Run the test script to verify everything works:

```bash
python test_web_search.py
```

This will create a sample web page using mock data to test the HTML generation.

### 4. Run the Web Aggregator

#### Individual Run
```bash
python aggregator_web.py
```

#### Include in Batch Run
The web aggregator is already included in `run_all_aggregators.py`, so running:
```bash
python run_all_aggregators.py
```
will process all categories including web search results.

## Configuration Options

### Search Query
Edit the `SEARCH_QUERY` variable in `aggregator_web.py` to customize what topics to search for:

```python
SEARCH_QUERY = "artificial intelligence breakthrough OR machine learning advancement OR AI research"
```

### Search Timeframe
Modify `SEARCH_TIMEFRAME` to change how recent articles should be:

```python
SEARCH_TIMEFRAME = "d1"  # Last 24 hours
# Options: d1 (1 day), w1 (1 week), m1 (1 month), y1 (1 year)
```

### Article Limit
Adjust `MAX_ARTICLES` to control how many articles to process:

```python
MAX_ARTICLES = 8  # Number of articles to process
```

## Output

The web aggregator generates:
- `output/web.html` - The main tech news page
- `output/images/article_*.jpg` - Associated images for articles

## Integration with Existing System

The web aggregator integrates seamlessly with your existing system:

1. **Uses same AI models**: Leverages your Ollama setup for content enhancement
2. **Same image system**: Uses your Unsplash API for visual content
3. **Same upload process**: Uses your FTP configuration for publishing
4. **Same tracking system**: Extends your existing seen IDs tracking
5. **Same template system**: Uses consistent newspaper-style design

## Customization Examples

### Different Search Topics

Create specialized aggregators for different topics:

```python
# Technology focus
SEARCH_QUERY = "technology innovation OR software development OR tech startup"

# Business focus  
SEARCH_QUERY = "business AI OR enterprise technology OR digital transformation"

# Science focus
SEARCH_QUERY = "scientific breakthrough OR research discovery OR innovation"
```

### Multiple Web Aggregators

You can create multiple web aggregators for different topics by:

1. Copying `aggregator_web.py` to `aggregator_web_business.py`
2. Modifying the search query and output filename
3. Adding it to `run_all_aggregators.py`

## Troubleshooting

### Common Issues

1. **"Web search is disabled" message**
   - Check that `GOOGLE_SEARCH_API_KEY` and `GOOGLE_SEARCH_ENGINE_ID` are set in `.env`
   - Verify the API key is valid and has Custom Search API enabled

2. **No articles found**
   - Check your search query isn't too restrictive
   - Verify your Custom Search Engine is configured to search the entire web
   - Try adjusting the `SEARCH_TIMEFRAME`

3. **API quota exceeded**
   - Google Custom Search has daily limits (100 queries/day for free tier)
   - Consider upgrading to paid tier or reducing `MAX_ARTICLES`

### Debug Mode

Enable verbose logging by setting:
```bash
export DEBUG=1
python aggregator_web.py
```

## Cost Considerations

- **Google Custom Search**: Free tier provides 100 queries/day
- **Unsplash API**: Free tier provides 50 requests/hour
- **Ollama**: Runs locally, no API costs
- **FTP**: Uses your existing server

For production use, consider upgrading to paid tiers if you hit rate limits.

## Next Steps

1. Test with mock data using `test_web_search.py`
2. Set up Google Custom Search API credentials
3. Run a single web aggregator to test
4. Include in your regular batch runs
5. Customize search queries for your specific interests
6. Consider creating multiple specialized web aggregators

The web search aggregator opens up possibilities for creating news pages on any topic you're interested in, not just academic research papers!
