#!/usr/bin/env python3
"""
Test script for the web search aggregator functionality.
This script demonstrates how to use the web search aggregator without requiring
all the API credentials to be set up.
"""

import os
import sys
from datetime import datetime

# Mock the web search functionality for testing
def mock_search_google_news(query, max_results=10):
    """Mock Google search results for testing purposes."""
    print(f"[MOCK] Searching for: {query}")
    
    # Return some sample articles
    mock_articles = [
        {
            'title': 'Revolutionary AI Model Achieves Human-Level Performance in Complex Reasoning Tasks',
            'snippet': 'Researchers at leading tech companies have developed a new artificial intelligence model that demonstrates unprecedented capabilities in logical reasoning and problem-solving. The breakthrough represents a significant step toward artificial general intelligence.',
            'url': 'https://example.com/ai-breakthrough-1',
            'source': 'TechCrunch',
            'published': '2024-01-15T10:30:00Z',
            'id': 'mock_article_1'
        },
        {
            'title': 'Machine Learning Algorithm Predicts Climate Change Effects with 95% Accuracy',
            'snippet': 'Scientists have created a sophisticated machine learning system that can predict regional climate change impacts with remarkable precision. The tool could revolutionize environmental planning and disaster preparedness.',
            'url': 'https://example.com/climate-ml-2',
            'source': 'Nature',
            'published': '2024-01-15T08:15:00Z',
            'id': 'mock_article_2'
        },
        {
            'title': 'Quantum Computing Breakthrough Enables Faster AI Training',
            'snippet': 'A new quantum computing architecture has been shown to dramatically accelerate machine learning model training. The hybrid quantum-classical approach could make advanced AI more accessible to researchers worldwide.',
            'url': 'https://example.com/quantum-ai-3',
            'source': 'MIT Technology Review',
            'published': '2024-01-15T06:45:00Z',
            'id': 'mock_article_3'
        },
        {
            'title': 'AI-Powered Drug Discovery Platform Identifies Promising Cancer Treatment',
            'snippet': 'Pharmaceutical researchers using artificial intelligence have identified a novel compound that shows exceptional promise in early cancer treatment trials. The AI system analyzed millions of molecular combinations in record time.',
            'url': 'https://example.com/ai-drug-discovery-4',
            'source': 'Science Daily',
            'published': '2024-01-14T22:30:00Z',
            'id': 'mock_article_4'
        },
        {
            'title': 'Autonomous Vehicles Achieve 99.9% Safety Record in Latest Tests',
            'snippet': 'Self-driving car technology has reached a new milestone with the latest generation of autonomous vehicles demonstrating near-perfect safety performance in comprehensive real-world testing scenarios.',
            'url': 'https://example.com/autonomous-vehicles-5',
            'source': 'Wired',
            'published': '2024-01-14T18:20:00Z',
            'id': 'mock_article_5'
        }
    ]
    
    return mock_articles[:max_results]

def test_web_aggregator():
    """Test the web aggregator functionality with mock data."""
    print("🧪 Testing Web Search Aggregator")
    print("=" * 50)
    
    # Import the aggregator module
    try:
        from aggregator_web import (
            load_seen_ids, save_seen_ids, generate_article_image,
            upload_via_ftp, MAX_ARTICLES, SEARCH_QUERY
        )
        from content_utils import rewrite_title, rewrite_blurb
        from generate_html import generate_html
        print("✅ Successfully imported aggregator modules")
    except ImportError as e:
        print(f"❌ Failed to import modules: {e}")
        return False
    
    # Mock the search function
    print(f"🔍 Mock searching for: {SEARCH_QUERY}")
    articles = mock_search_google_news(SEARCH_QUERY, MAX_ARTICLES * 2)
    
    if not articles:
        print("❌ No articles found from mock search")
        return False
    
    print(f"✅ Found {len(articles)} mock articles")
    
    # Process articles (without AI rewriting for this test)
    processed = []
    
    for idx, art in enumerate(articles[:MAX_ARTICLES]):
        print(f"📄 Processing article {idx + 1}: {art['title'][:50]}...")
        
        # For testing, we'll skip the AI rewriting and image generation
        # In real usage, you would call:
        # new_summary = rewrite_blurb(art['title'], art['snippet'], "technology news")
        # new_headline = rewrite_title(art['title'], "technology news", art['snippet'], new_summary)
        
        article_data = {
            'id': art['id'],
            'title': art['title'],  # Using original title for test
            'blurb': art['snippet'],  # Using original snippet for test
            'url': art['url'],
            'source': art['source'],
            'featured': idx == 0  # Make first article featured
        }
        
        processed.append(article_data)
    
    # Generate HTML
    print("🌐 Generating HTML...")
    html_content = generate_html(processed, category="Tech News")
    
    # Save to output directory
    os.makedirs('output', exist_ok=True)
    output_path = os.path.join('output', 'web_test.html')
    
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"✅ Generated test HTML at {output_path}")
    print(f"📊 Processed {len(processed)} articles")
    
    # Show sample of generated content
    print("\n📋 Sample of generated content:")
    print("-" * 30)
    for i, art in enumerate(processed[:2]):
        print(f"{i+1}. {art['title']}")
        print(f"   Source: {art['source']}")
        print(f"   URL: {art['url']}")
        print()
    
    return True

def main():
    """Main test function."""
    print(f"🚀 Web Search Aggregator Test - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Check if we're in the right directory
    if not os.path.exists('aggregator_web.py'):
        print("❌ Please run this script from the arxiv_aggregator directory")
        return 1
    
    # Run the test
    success = test_web_aggregator()
    
    if success:
        print("\n🎉 Test completed successfully!")
        print("💡 To use real web search, set up Google Custom Search API credentials in your .env file")
        return 0
    else:
        print("\n❌ Test failed")
        return 1

if __name__ == '__main__':
    sys.exit(main())
